cmake_minimum_required(VERSION 3.8)

set(TARGET_NAME rcb)

set(CMAKE_EXPORT_COMPILE_COMMANDS 1)

add_executable(
    ${TARGET_NAME}
    src/main.cpp
    lib/inverter/src/inverter_meanwell.cpp
    lib/battery/src/battery_dalybms.cpp
    lib/battery/src/battery_jkbms.cpp
    lib/battery/src/battery.cpp
    lib/power_systems/src/power_system.cpp
    lib/liftkit/src/liftkit.cpp
    lib/utils/src/utils.cpp
    lib/wheel_control/src/wheel_control.cpp
    lib/kinco/src/kinco_servo_control.cpp
    lib/can/src/can.cpp
    lib/tower_light/src/tower_light.cpp
    ${CMAKE_SOURCE_DIR}/linuxlibs/src/canlinux.cpp
    ${CMAKE_SOURCE_DIR}/linuxlibs/src/relay_control.cpp
    ${CMAKE_SOURCE_DIR}/../../embedded_common/uros/src/uros.cpp
)

target_compile_definitions(
    ${TARGET_NAME}
    PUBLIC
    ROS_DOMAIN_ID=13
    # DEBUG_PRINTS
)

target_include_directories(
    ${TARGET_NAME} PRIVATE
    include/
    lib/utils/include
    lib/relay_control/include
    lib/liftkit/include
    lib/kinco/include
    lib/battery/include
    lib/inverter/include
    lib/power_systems/include
    lib/can/include
    lib/tower_light/include
    lib/wheel_control/include
    ${CMAKE_SOURCE_DIR}/linuxlibs/include
    ${CMAKE_SOURCE_DIR}/microros/lib/include
    ${CMAKE_SOURCE_DIR}/../../embedded_common/pinctrl/include
    ${CMAKE_SOURCE_DIR}/../../embedded_common/uros/include
)

set(
    MICROROS_INCLUDES
    actionlib_msgs
    action_msgs
    builtin_interfaces
    composition_interfaces
    diagnostic_msgs
    example_interfaces
    geometry_msgs
    lifecycle_msgs
    micro_ros_msgs
    micro_ros_utilities
    nav_msgs
    rcl
    rcl_action
    rclc
    rclc_lifecycle
    rclc_parameter
    rcl_interfaces
    rcl_lifecycle
    rcl_logging_interface
    rcutils
    rmw
    rmw_microros
    rmw_microxrcedds_c
    rosgraph_msgs
    rosidl_dynamic_typesupport
    rosidl_runtime_c
    rosidl_typesupport_c
    rosidl_typesupport_interface
    rosidl_typesupport_introspection_c
    rosidl_typesupport_microxrcedds_c
    sensor_msgs
    service_msgs
    shape_msgs
    statistics_msgs
    std_msgs
    std_srvs
    stereo_msgs
    test_msgs
    tracetools
    trajectory_msgs
    type_description_interfaces
    ucdr
    unique_identifier_msgs
    uxr
    visualization_msgs
    vision_msgs
    custom_msgs
)

foreach(dir IN LISTS MICROROS_INCLUDES)
    target_include_directories(
        ${TARGET_NAME}
        PRIVATE ${CMAKE_SOURCE_DIR}/microros/lib/include/${dir}
    )
endforeach()

target_link_libraries(
    ${TARGET_NAME}
    PUBLIC ${MICROROS_STATIC_LIB}
    PRIVATE etl::etl
)

install(
    TARGETS ${TARGET_NAME}
    RUNTIME DESTINATION bin
    COMPONENT runtime
)

