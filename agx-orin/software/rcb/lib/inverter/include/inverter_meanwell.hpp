#pragma once
#include <stdint.h>

#include "config.hpp"
#include "can.hpp"

#define MEANWELL_REQUEST_FRAME_ID 0x000C0501
#define MEANWELL_RESPONSE_FRAME_ID 0x000C0401

#define MEANWELL_INVERTER_DATA_LENGTH 0X02

#define INVERTER_VOLTAGE__CMD_HIGH 0x1A
#define INVERTER_VOLTAGE__CMD_LOW 0x01
#define INVERTER_VOLTAGE__SCALING_FACTOR 0.01f 

#define INVERTER_OPERATION_CMD_HIGH 0x00
#define INVERTER_OPERATION_CMD_LOW 0x01
#define INVERTER_DISABLE 0x06
#define INVERTER_ENABLE 0x05



#define MEANWELL_BATTERY_MIN_VOLTAGE 48.0f  // 48V = 0%
#define MEANWELL_BATTERY_MAX_VOLTAGE 52.4f  // 52.4V = 100%

void request_meanwell_inverter_information();
void parse_meanwell_inverter_message(
    const uint32_t can_id,
    CanDevice::RecvDataBuffer_t message,
    float msg_actuation_battery_ros[3]
);
float convert_meanwell_data_to_float(const uint8_t high_byte, const uint8_t low_byte, float scaling_factor);
float calculate_battery_capacity_from_voltage(float voltage);
void disable_meanwell_inverter(bool disable);
