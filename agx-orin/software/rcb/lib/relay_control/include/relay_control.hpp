#pragma once

#include <cstdint>

class Relay {
    protected:
        uint8_t m_pin;
        bool m_state;
        explicit Relay(uint8_t pin);
        virtual ~Relay() = default;
    public:
        virtual void enable() = 0;
        virtual void disable() = 0;
        void toggle();
};

class ActiveHighRelay: public Relay {
    public:
        explicit ActiveHighRelay(uint8_t pin);
        void enable() override;
        void disable() override;
};


class ActiveLowRelay: public Relay {
    public:
        explicit ActiveLowRelay(uint8_t pin);
        void enable() override;
        void disable() override;
};

