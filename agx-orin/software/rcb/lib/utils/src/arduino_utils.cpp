#include <Arduino.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Provides a POSIX-like usleep function for compatibility.
 * @param microseconds The number of microseconds to sleep.
 * @return Always returns 0.
 */
int usleep(unsigned int microseconds) {
    delayMicroseconds(microseconds);
    return 0; // Standard usleep returns 0 on success
}

int sleep(unsigned int seconds) {
    delay(seconds * 1000);
    return 0; // Standard usleep returns 0 on success
}

#ifdef __cplusplus
}
#endif

