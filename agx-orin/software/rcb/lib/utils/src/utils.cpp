#include <stdarg.h>
#include <stdint.h>

#include "etl/array_wrapper.h"

#include "utils.hpp"
#include "kinco_can_open_object_dictionary.hpp"
#include "can.hpp"

namespace CanUtils {

void populateCanMessageFields(
        CanDevice::SendDataBuffer_t dataBuffer,
        const eOperationType operationType,
        const eMessageDataType dataType,
        const uint16_t index,
        const uint8_t subIndex,
        const uint32_t data) {

  switch (operationType) {
  case eOperationType::DOWNLOAD:
    switch (dataType) {
    case eMessageDataType::U8:
    case eMessageDataType::I8:
      dataBuffer[0] = DOWNLOAD_COMMAND_WORD_8_BITS;
      break;
    case eMessageDataType::U16:
    case eMessageDataType::I16:
      dataBuffer[0] = DOWNLOAD_COMMAND_WORD_16_BITS;
      break;
    case eMessageDataType::U32:
    case eMessageDataType::I32:
      dataBuffer[0] = DOWNLOAD_COMMAND_WORD_32_BITS;
      break;
    default:
      // Serial.println("Cannot Handle DataType");
      break;
    }
    break;

  case eOperationType::SDO_READ:
    dataBuffer[0] = SDO_READ_SEND_PARAMETER_CMD_WORD;
    break;

  default:
    // Serial.println("Unknown Operation Type!");
    break;
  }
  dataBuffer[1] = index & 0xFF;
  dataBuffer[2] = (index >> 8) & 0xFF;
  dataBuffer[3] = subIndex;
  dataBuffer[4] = data & 0xFF;
  dataBuffer[5] = (data >> 8) & 0xFF;
  dataBuffer[6] = (data >> 16) & 0xFF;
  dataBuffer[7] = (data >> 24) & 0xFF;
}

float interpret_can_bytes_to_decimal_big_endian(const float resolution,
                                                const float offset,
                                                const int count, ...) {
  va_list args;
  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    hexValue |= va_arg(args, int) << (8 * idx);
  }
  float converted_value = (float)hexValue * resolution;
  converted_value += offset;
  return converted_value;
}


float interpret_can_bytes_to_decimal_little_endian(const float resolution,
                                                   const float offset,
                                                   const int count, ...) {
  va_list args;
  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    hexValue |= va_arg(args, int) << (8 * (count - idx - 1));
  }
  float converted_value = (float)hexValue * resolution;
  converted_value += offset;
  return converted_value;
}

// Daly BMS Provides offsets as negative numbers
// The offset needs to be subtracted before the resolution is multiplied
float interpret_can_bytes_to_decimal_little_endian_daly_bms(
    const float resolution, const float offset, const int count, ...) {
  va_list args;

  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    const uint8_t hex_val = va_arg(args, int);
    hexValue |= hex_val << (8 * (count - idx - 1));
  }
  float converted_value = (float)hexValue;
  converted_value -= offset;
  converted_value = converted_value * resolution;
  return converted_value;
}

}

