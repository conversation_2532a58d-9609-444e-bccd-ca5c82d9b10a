#pragma once
#include <cstdint>

#include "etl/array_wrapper.h"

#include "can.hpp"

namespace CanUtils {

enum class eMessageDataType {
    U8,
    U16,
    U32,
    I8,
    I16,
    I32 
};

enum class eOperationType {
    DOWNLOAD,
    SDO_READ
};


void populateCanMessageFields(
    CanDevice::SendDataBuffer_t dataBuffer,
    eOperationType operationType,
    eMessageDataType dataType,
    uint16_t index,
    uint8_t subIndex,
    uint32_t data
);


float interpret_can_bytes_to_decimal_big_endian(const float resolution,
                                                const float offset,
                                                const int count, ...);

/* @brief Convert given data of length to big endian representation and
 * return after storing into given type
 * */
template <typename T>
T bytes_to_int_little_endian(std::size_t len, uint8_t *data) {
    T hexValue = 0;
    for (uint8_t idx = 0; idx < len; idx++) {
        hexValue |= data[idx] << (8 * idx);
    }
    return hexValue;
}

template <typename I, typename O>
O valToEngUnitDEC(I value, uint32_t resolution) {
    return static_cast<O>(
            (value * 512.0 * resolution) / 1875.0
    );
}

template <typename I, typename O>
O valFromEngUnitDEC(I dec, uint32_t resolution) {
    return static_cast<O>(
            (dec * 1875.0) / (512.0 * resolution)
    );
}


float interpret_can_bytes_to_decimal_little_endian(const float resolution,
                                                   const float offset,
                                                   const int count, ...);
float interpret_can_bytes_to_decimal_little_endian_daly_bms(
    const float resolution, const float offset, const int count, ...);
}
