#include <memory>
#include <cstring>

#include <ACAN_T4.h>
#include <ACAN_T4_CANMessage.h>
#include <ACAN_T4_CANFDMessage.h>

#include "can.hpp"
#include "canteensy.hpp"


CanTeensy::CanTeensy(uint8_t channelIndex, uint32_t baud)
    :
        CanDevice::CanDevice(channelIndex, baud)
{
    switch (m_channelIndex)
    {
        case 1:
        {
            m_can = &ACAN_T4::can1;
            break;
        }
        case 2:
        {
            m_can = &ACAN_T4::can2;
            break;
        }
        case 3:
        {
            m_can = &ACAN_T4::can3;
            break;
        }

    }
}

auto CanTeensy::connect() -> bool
{
    ACAN_T4_Settings canSettings(m_baud);
    return (0 == m_can->begin(canSettings));
}

auto CanTeensy::sendLastPopulatedData(
    uint32_t id,
    size_t len,
    bool extend,
    bool rtr
    ) -> bool
{
    m_messageSendBuffer.id = id;
    m_messageSendBuffer.len = len;
    m_messageSendBuffer.ext = extend;
    m_messageSendBuffer.rtr = rtr;
    return m_can->tryToSend(m_messageSendBuffer);
}

auto CanTeensy::getSendDataBuffer()
    -> SendDataBuffer_t
{
    return m_messageSendBuffer.data;
}

auto CanTeensy::resetSendDataBuffer() -> void
{
    std::memset(
        this->getSendDataBuffer().begin(),
        0,
        this->getSendDataBuffer().size()
    );
}

auto CanTeensy::getRecvDataBuffer()
    -> RecvDataBuffer_t
{
    return m_messageRecvBuffer.data;
}

auto CanTeensy::getRecvId() -> uint32_t
{
    return m_messageRecvBuffer.id;
}

auto CanTeensy::dataAvailable() -> bool
{
    if (m_can->available())
    {
        m_can->receive(m_messageRecvBuffer);
        return true;
    }
    return false;
}


auto CanDevice::getActuationCan() -> CanDevice*
{
    static CanTeensy instance(1, 1000*1000);
    return static_cast<CanDevice*>(&instance);
}

auto CanDevice::getPowerCan() -> CanDevice*
{
    static CanTeensy instance(2, 250*1000);
    return static_cast<CanDevice*>(&instance);
}


