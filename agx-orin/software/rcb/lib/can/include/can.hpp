#pragma once
#include <cstdint>
// #include <functional>
// #include <optional>

#include "etl/span.h"

class CanDevice
{
    protected:
        uint8_t m_channelIndex;
        uint32_t m_baud;

        // auto getCallbackForId(CanId_t) -> std::optional<CanExpectationCallback_t&>;

        CanDevice(uint8_t channelIndex, uint32_t baud);
    public:
        ~CanDevice() = default;
        CanDevice(CanDevice &) = delete;
        CanDevice(CanDevice &&) = delete;

        static const size_t SEND_BUFFER_SIZE = 8;   // Std CAN message
        static const size_t RECV_BUFFER_SIZE = 8;   // Std CAN message
                                                    //
                                                    // using CanId_t = uint32_t;
        using SendDataBuffer_t = etl::span<uint8_t, SEND_BUFFER_SIZE>;
        using RecvDataBuffer_t = etl::span<uint8_t, RECV_BUFFER_SIZE>;
        // using CanExpectationCallback_t = std::function<bool(CanId_t, RecvDataBuffer_t)>;

        virtual auto connect() -> bool = 0;
        virtual auto sendLastPopulatedData(
            uint32_t id,
            size_t len,
            bool extend=false,
            bool rtr=false
        ) -> bool = 0;
        virtual auto resetSendDataBuffer() -> void = 0;
        virtual auto getSendDataBuffer() -> SendDataBuffer_t = 0;
        /* @brief Checks if undelying implementation has data available
         * and reads it into an internal message buffer, if true.
         * Will return false if data is not available.
         * Will return true after reading data into buffer if available.
         * Will overwrite previous data if called again.
         * */
        virtual auto dataAvailable() -> bool = 0;
        /* @brief Gets the data buffer in which incoming data will be read.
         * Max size - 10 bytes.
         * */
        virtual auto getRecvDataBuffer() -> RecvDataBuffer_t = 0;
        virtual auto getRecvId() -> uint32_t = 0;

        // virtual auto addResponseExpectation(CanId_t expectedId, CanExpectationCallback_t) -> bool = 0;
        // virtual auto spinOnce(std::chrono::nanoseconds &timeout) -> void = 0;

        static auto getActuationCan() -> CanDevice*;
        static auto getPowerCan() -> CanDevice*;
        static auto getCan(const char * name=nullptr) -> CanDevice*;
};
