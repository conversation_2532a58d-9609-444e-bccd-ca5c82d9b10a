#pragma once
#include <cstdint>

#include <ACAN_T4.h>
#include <ACAN_T4_CANMessage.h>

#include "can.hpp"

class CanTeensy: public CanDevice
{
    private:
        ACAN_T4 * m_can;
        CANMessage m_messageSendBuffer;
        CANMessage m_messageRecvBuffer;
        CanTeensy() = default;
    public:
        ~CanTeensy() = default;
        CanTeensy(CanTeensy &) = delete;
        CanTeensy(CanTeensy &&) = delete;
        CanTeensy(uint8_t channelIndex, uint32_t baud);
        auto connect() -> bool override;
        auto resetSendDataBuffer() -> void override;
        auto sendLastPopulatedData(
            uint32_t id,
            size_t len,
            bool extend=false,
            bool rtr=false
        ) -> bool override;
        auto getSendDataBuffer() -> CanDevice::SendDataBuffer_t override;
        auto dataAvailable() -> bool override;
        auto getRecvDataBuffer() -> CanDevice::RecvDataBuffer_t override;
        auto getRecvId() -> uint32_t override;
};
