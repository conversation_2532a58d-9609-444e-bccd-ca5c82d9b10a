#include <cstdint>
#include <chrono>
#include <thread>

#include "kinco_servo_control.hpp"
#include "kinco_can_open_object_dictionary.hpp"
#include "utils.hpp"
#include "can.hpp"
#include "wheel_control.hpp"

#define TESTING_MODE_SPEED 500.0f

// ---------------------------------------------------//
// Kinco Motor Base Class
// ---------------------------------------------------//

kincoServoControl::kincoServoControl(const uint8_t node_id,
                                     const uint16_t heartbeatTime_ms,
                                     const float acc_dec_conversion_factor,
                                     const float pos_vel_conversion_factor,
                                     const float min_value,
                                     const float max_value)
    : m_heartBeatTime_ms(heartbeatTime_ms), m_motorNodeID(node_id),
      m_acc_dec_conversion_factor(acc_dec_conversion_factor),
      m_pos_vel_conversion_factor(pos_vel_conversion_factor),
      m_minValue(min_value), m_maxValue(max_value) {}

void kincoServoControl::m_setNodeHeartbeatTime(
    const uint8_t nodeID, const uint16_t heartBeatTime_ms) {
  // wheelControl::prepareAndSendMessage(
  //     CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U16,
  //     PRODUCER_HEARTBEAT_TIME_IDX, PRODUCER_HEARTBEAT_TIME_SUB_IDX,
  //     heartBeatTime_ms);
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}

void kincoServoControl::m_emergencyStopWheel() {
  {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U16,
        CONTROLWORD_IDX, CONTROLWORD_SUB_IDX, CONTROLWORD_DRIVER_OFF);
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
  {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U16,
        CONTROLWORD_IDX, CONTROLWORD_SUB_IDX, CONTROLWORD_DRIVER_OFF);
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
}

void kincoServoControl::m_setProfileAcceleration_RPS(
    const float profileAccel_RPS, const float profileDeaccel_RPS) {
  {
    float profileAccel_RPS_decimal =
        profileAccel_RPS * this->m_acc_dec_conversion_factor;
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U32,
        PROFILE_ACC_IDX, PROFILE_ACC_SUB_IDX, profileAccel_RPS_decimal);
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
  {
    float profileDeaccel_RPS_decimal =
        profileDeaccel_RPS * this->m_acc_dec_conversion_factor;
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U32,
        PROFILE_DEC_IDX, PROFILE_DEC_SUB_IDX, profileDeaccel_RPS_decimal);
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
}

void kincoServoControl::m_setProfileSpeed_RPM(float profileSpeed_RPM) {
  this->m_enableMotor(false);
  float profileSpeed_RPM_decimal =
      profileSpeed_RPM * m_pos_vel_conversion_factor;
  wheelControl::prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U32,
      PROFILE_SPEED_IDX, PROFILE_SPEED_SUB_IDX, profileSpeed_RPM_decimal);
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}

bool kincoServoControl::m_checkWithinLimits(const float requested_value) {
  return (requested_value <= this->m_maxValue && requested_value >= this->m_minValue);
}


void kincoServoControl::requestEncoderReadings() {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::SDO_READ,
        m_motorNodeID,
        CanUtils::eMessageDataType::I32,
        POS_ACTUAL_IDX,
        POS_ACTUAL_SUB_IDX,
        0
    );
}

void kincoServoControl::parseEncoderReading(size_t dataLen, uint8_t *data) {
    m_latestEncoderEnc = CanUtils::bytes_to_int_little_endian<int32_t>(
        dataLen,
        data
    );
}



// ---------------------------------------------------//
// Velocity Control Motor
// ---------------------------------------------------//

kincoMotorVelocityControlMode::kincoMotorVelocityControlMode(
    const uint8_t node_id, const uint16_t heatbeatTime_ms,
    const float acc_dec_conversion_factor,
    const float pos_vel_conversion_factor, const float min_speed,
    const float max_speed)
    : kincoServoControl(node_id, heatbeatTime_ms, acc_dec_conversion_factor,
                        pos_vel_conversion_factor, min_speed, max_speed) {}

void kincoMotorVelocityControlMode::m_initializeMotor(bool invert_direction) {
  // Serial.print("Travel Mode Motor ID: 0x");
  // Serial.println(this->m_motorNodeID, HEX);

  // Set Producer_Heartbeat_Time
  this->m_setNodeHeartbeatTime(this->m_motorNodeID, this->m_heartBeatTime_ms);

  // Invert Motor Direction
  if (invert_direction) {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U8,
        INVERT_DIR_IDX, INVERT_DIR_SUB_IDX, INVERT_DIR_CW);
    this->m_motorDirection = INVERT_DIR_CW;
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  } else {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U8,
        INVERT_DIR_IDX, INVERT_DIR_SUB_IDX, INVERT_DIR_CCW);
    this->m_motorDirection = INVERT_DIR_CCW;
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }

  // Set Operation_Mode to Speed Control
  {
    wheelControl::prepareAndSendMessage(
            CanUtils::eOperationType::DOWNLOAD,
            this->m_motorNodeID,
            CanUtils::eMessageDataType::I8,
            OPERATION_MODE_IDX,
            OPERATION_MODE_SUB_IDX,
            OPERATION_MODE_SPEED_RAMP
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }

  this->m_enableMotor(false);
}

void kincoMotorVelocityControlMode::m_enableMotor(bool enable) {
  uint8_t controlword = CONTROLWORD_SPEED_MODE_ENABLE;
  if (!enable) {
    controlword = CONTROLWORD_DRIVER_OFF;
  }

  wheelControl::prepareAndSendMessage(
          CanUtils::eOperationType::DOWNLOAD,
          this->m_motorNodeID,
          CanUtils::eMessageDataType::U16,
          CONTROLWORD_IDX,
          CONTROLWORD_SUB_IDX,
          controlword
  );
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}

void kincoMotorVelocityControlMode::m_setMotorSpeed_RPM(
    const float travelSpeed) {

  if (!this->m_checkWithinLimits(travelSpeed)) {
    return;
  }

  if (this->m_currentSpeed_RPM == travelSpeed) {
    return;
  }
  this->m_currentSpeed_RPM = travelSpeed;

  const int32_t speed_rpm_decimal =
      (int32_t)(travelSpeed * m_travel_pos_vel_conversion_factor);

  uint8_t newDirection = (this->m_motorDirection == INVERT_DIR_CW)
                             ? INVERT_DIR_CW
                             : INVERT_DIR_CCW;
  {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        this->m_motorNodeID,
        CanUtils::eMessageDataType::U8,
        INVERT_DIR_IDX,
        INVERT_DIR_SUB_IDX,
        newDirection
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
  {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        this->m_motorNodeID,
        CanUtils::eMessageDataType::I32,
        TARGET_SPEED_IDX,
        TARGET_SPEED_SUB_IDX,
        speed_rpm_decimal
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }

  this->m_enableMotor(true);
}

// ---------------------------------------------------//
// Position Control Motor
// ---------------------------------------------------//
kincoMotorPositionControlMode::kincoMotorPositionControlMode(
    const uint8_t node_id,
    const int32_t motor_homing_offset,
    const uint16_t heatbeatTime_ms,
    const float acc_dec_conversion_factor,
    const float pos_vel_conversion_factor,
    const float min_position,
    const float max_position)
    : kincoServoControl(node_id, heatbeatTime_ms, acc_dec_conversion_factor,
                        pos_vel_conversion_factor, min_position, max_position),
      m_homingEncoderOffset_Decimal(motor_homing_offset) {}

void kincoMotorPositionControlMode::m_initializeMotor(bool invert_direction)
{
  // Serial.print("Position Mode Motor ID: 0x");
  // Serial.println(this->m_motorNodeID, HEX);
  // Set Producer_Heartbeat_Time
  this->m_setNodeHeartbeatTime(this->m_motorNodeID, this->m_heartBeatTime_ms);

  // Set Operation_Mode to Position Control
  {
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        this->m_motorNodeID,
        CanUtils::eMessageDataType::I8,
        OPERATION_MODE_IDX,
        OPERATION_MODE_SUB_IDX,
        OPERATION_MODE_POSITION
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }

  this->m_enableMotor(false);
  this->m_setProfileAcceleration_RPS(
          this->m_profilAcceleration_RPS,
          this->m_profilDecceleration_RPS
  );
  this->m_setProfileSpeed_RPM(this->m_profileSpeed_RPM);
  this->m_setMotorAngle_Degrees(0.0f);
}

void kincoMotorPositionControlMode::setProfileSpeedRPM(
        float profileSpeedRPM) {
    m_profileSpeed_RPM = profileSpeedRPM;
}

void kincoMotorPositionControlMode::m_enableMotor(bool enable) {
  uint8_t controlword = CONTROLWORD_POSITION_MODE_ABSOLUTE_ENABLE;
  if (!enable) {
    controlword = CONTROLWORD_POSITION_MODE_ABSOLUTE_OFF;
  }

  wheelControl::prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD, this->m_motorNodeID, CanUtils::eMessageDataType::U16,
      CONTROLWORD_IDX, CONTROLWORD_SUB_IDX, controlword);
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}


int32_t kincoMotorPositionControlMode::getLatestEnc() {
    return m_latestEncoderEnc - m_homingEncoderOffset_Decimal;
}

void kincoMotorPositionControlMode::setMotorEncoderCounts(int32_t enc) {

    this->m_enableMotor(false);
    wheelControl::prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        this->m_motorNodeID,
        CanUtils::eMessageDataType::I32,
        TARGET_POSITION_IDX,
        TARGET_POSITION_SUB_IDX,
        (enc + m_homingEncoderOffset_Decimal)
    );

    this->m_enableMotor(true);

}

bool kincoMotorPositionControlMode::m_setMotorAngle_Degrees(
    const float absoluteSteeringAngle_Degrees) {

  if (!this->m_checkWithinLimits(absoluteSteeringAngle_Degrees))
      return false;

  if (this->m_currentAngle_deg == absoluteSteeringAngle_Degrees)
    return true;

  this->m_enableMotor(false);

  const int32_t steerPosition_dec = static_cast<int32_t>(
    (
        this->m_motorDirection
        * CanUtils::valToEngUnitDEC<float, int32_t>(
            absoluteSteeringAngle_Degrees,
            STEERING_ENCODER_RESOLUTION
        )
    )
    + this->m_homingEncoderOffset_Decimal
  );

  wheelControl::prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD,
      this->m_motorNodeID,
      CanUtils::eMessageDataType::I32,
      TARGET_POSITION_IDX,
      TARGET_POSITION_SUB_IDX,
      steerPosition_dec
   );

   std::this_thread::sleep_for(CAN_MSG_DELAY);

   this->m_enableMotor(true);
   this->m_currentAngle_deg = absoluteSteeringAngle_Degrees;
   return true;
}

