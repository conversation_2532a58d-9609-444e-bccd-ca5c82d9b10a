#include <cstdint>

#include "relay_control.hpp"
#include "tower_light.hpp"
#include "rcb_pinout.hpp"

ActiveHighRelay redLight(eTowerLightPins::RED);
ActiveHighRelay yellowLight(eTowerLightPins::YELLOW);
ActiveHighRelay greenLight(eTowerLightPins::GREEN);
ActiveHighRelay blueLight(eTowerLightPins::BLUE);
ActiveHighRelay buzzer(eTowerLightPins::BUZZER);

// Scoped only to the File
enum class eTowerLightPinState
{
    OFF,
    ON,
    BLINKING
};

static void set_tower_light_pin_state(
    ActiveHighRelay lightControl,
    eTowerLightPinState pin_state
    )
{
    switch (pin_state)
    {
        case eTowerLightPinState::OFF:
        {
            lightControl.disable();
            break;
        }
        case eTowerLightPinState::ON:
        {
            lightControl.enable();
            break;
        }
        default:
        {
            // Serial.println("[TOWER LIGHT] Pin State Unknown");
            break;
        }
    }
}

static void set_tower_lights(
    eTowerLightPinState red_light_state,
    eTowerLightPinState buzzer_state,
    eTowerLightPinState yellow_light_state,
    eTowerLightPinState green_light_state,
    eTowerLightPinState blue_light_state
    )
{
  set_tower_light_pin_state(redLight, red_light_state);
  set_tower_light_pin_state(buzzer, buzzer_state);
  set_tower_light_pin_state(yellowLight, yellow_light_state);
  set_tower_light_pin_state(greenLight, green_light_state);
  set_tower_light_pin_state(blueLight, blue_light_state);
}

void set_tower_light_state(eTowerLightState robot_state) {
  switch (robot_state) {

  case eTowerLightState::INITIALIZING_MODE:
    set_tower_lights(eTowerLightPinState::ON, eTowerLightPinState::OFF,
                     eTowerLightPinState::ON, eTowerLightPinState::OFF,
                     eTowerLightPinState::OFF);
    break;

  case eTowerLightState::INITIALIZED_MODE:
    set_tower_lights(eTowerLightPinState::OFF, eTowerLightPinState::OFF,
                     eTowerLightPinState::ON, eTowerLightPinState::OFF,
                     eTowerLightPinState::OFF);

    break;
  case eTowerLightState::WIFI_CONNECTING:
    set_tower_lights(eTowerLightPinState::OFF, eTowerLightPinState::OFF,
                     eTowerLightPinState::BLINKING,
                     eTowerLightPinState::OFF, eTowerLightPinState::OFF);
    break;

  case eTowerLightState::ACTIVE_MODE:
    set_tower_lights(eTowerLightPinState::OFF, eTowerLightPinState::OFF,
                     eTowerLightPinState::OFF, eTowerLightPinState::ON,
                     eTowerLightPinState::OFF);
    break;

    // TODO: Switch from GREEN TO BLUE Light When the New Tower Light arrive
  case eTowerLightState::AUTONOMOUS_MODE:
    set_tower_lights(eTowerLightPinState::OFF, eTowerLightPinState::OFF,
                     eTowerLightPinState::OFF, eTowerLightPinState::ON,
                     eTowerLightPinState::ON);
    break;

  case eTowerLightState::ERROR_MODE:
    set_tower_lights(eTowerLightPinState::ON, eTowerLightPinState::ON,
                     eTowerLightPinState::OFF, eTowerLightPinState::OFF,
                     eTowerLightPinState::OFF);
    break;

    // Kept at last since this gets called only once
  case eTowerLightState::UNINITIALIZED_MODE:
    set_tower_lights(eTowerLightPinState::OFF, eTowerLightPinState::OFF,
                     eTowerLightPinState::OFF, eTowerLightPinState::OFF,
                     eTowerLightPinState::OFF);
    break;

  default:
    // Serial.println("[TOWER LIGHT]: Unknown Tower Light State Called");
    break;
  }
}
