#include "power_system.hpp"
#include "battery.hpp"
#include "battery_dalybms.hpp"
#include "battery_jkbms.hpp"
#include "inverter_meanwell.hpp"
#include "config.hpp"
#include "logging.hpp"
#include <chrono>
#include <thread>

// Global power readings message
static custom_msgs__msg__PowerReadings g_power_readings;
static bool g_battery_data_available = false;
static bool g_inverter_data_available = false;

void power_system_init() {
    // Initialize the PowerReadings message
    custom_msgs__msg__PowerReadings__init(&g_power_readings);
    
    // Initialize all values to zero
    g_power_readings.battery_voltage = 0.0f;
    g_power_readings.battery_current = 0.0f;
    g_power_readings.battery_soc = 0.0f;
    g_power_readings.inverter_voltage = 0.0f;
    g_power_readings.inverter_soc = 0.0f;
    
    g_battery_data_available = false;
    g_inverter_data_available = false;
    
    Log("Power System initialized");
}

void request_power_system_information() {
    // Request battery information
    #if BATTERY_BMS == DALYBMS
    request_battery_information();
    #endif
    
    // Add small delay between requests
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // Request inverter information
    request_meanwell_inverter_information();
    
#ifdef TESTING_MODE
    Log("Power system information requested");
#endif
}

void parse_power_system_message(
    const uint32_t can_id,
    CanDevice::RecvDataBuffer_t message,
    custom_msgs__msg__PowerReadings* power_readings
) {
    if (power_readings == nullptr) {
        power_readings = &g_power_readings;
    } 

    // Check if this is an inverter message FIRST (to avoid false positive with Daly BMS)
    if (can_id == MEANWELL_RESPONSE_FRAME_ID) {
        
        // Parse inverter data directly into PowerReadings structure
        uint8_t data_high = message[2];
        uint8_t data_low = message[3];
        
        float voltage = convert_meanwell_data_to_float(
            data_high,
            data_low,
            INVERTER_VOLTAGE__SCALING_FACTOR
        );

        power_readings->inverter_voltage = voltage;
        power_readings->inverter_soc = calculate_battery_capacity_from_voltage(voltage);

        g_inverter_data_available = true;

#ifdef TESTING_MODE
        Log("INVERTER RESPONSE DETECTED!");
        Log("Raw inverter data - High: 0x");
        Log(data_high);
        Log(" Low: 0x");
        Log(data_low);     

        Log("Calculated voltage: ");
        Log(voltage);

        Log("Inverter data updated - V:");
        Log(power_readings->inverter_voltage);
        Log(" SOC:");
        Log(power_readings->inverter_soc);
        Log("Inverter data available flag: ");
        Log(g_inverter_data_available);

        // Verify global structure is updated
        Log("Global structure check - V:");
        Log(g_power_readings.inverter_voltage);
        Log(" SOC:");
        Log(g_power_readings.inverter_soc);
#endif

        return;
    }

    // Check if this is a battery message
    bool is_battery_message = false;

    #if BATTERY_BMS == DALYBMS
    // Check for Daly BMS message format
    const uint8_t bms_address = DALY_BMS_GET_BMS_ADDRESS(can_id);
    const uint8_t data_id = DALY_BMS_GET_DATA_ID(can_id);

#ifdef TESTING_MODE
    Log("Daly BMS check - BMS Address: ");
    Log(bms_address);
    Log(" Data ID: ");
    Log(data_id);
    Log(" Expected BMS Address: ");
    Log(ACTUATION_SYSTEM_BATTERY_BMS_ADDRESS);
#endif

    // Check if this matches Daly BMS format
    if (bms_address == ACTUATION_SYSTEM_BATTERY_BMS_ADDRESS) {
        is_battery_message = true;
        
        switch (data_id) {
        case (uint8_t)DALY_BMS_DID::SOC_DATA_ID: {
            // Parse battery data directly into PowerReadings structure
            float temp_battery_data[3];
            parse_battery_soc_data(message, bms_address, temp_battery_data);
            
            power_readings->battery_voltage = temp_battery_data[0];
            power_readings->battery_current = temp_battery_data[1];
            power_readings->battery_soc = temp_battery_data[2];
            
            g_battery_data_available = true;
            
            Log("Battery data updated - V:");
            Log(power_readings->battery_voltage);
            Log(" I:");
            Log(power_readings->battery_current);
            Log(" SOC:");
            Log(power_readings->battery_soc);
            break;
        }
        default:
            // Handle other battery message types if needed
            break;
        }
    }
    #elif BATTERY_BMS == JKBMS
    // Check for JK BMS message format
    switch (can_id) {
    case (uint32_t)JKBMS_DID::BATT_ST_1:
        is_battery_message = true;
        read_battery_status_1(
            message,
            &power_readings->battery_voltage,
            &power_readings->battery_current,
            &power_readings->battery_soc
        );
        g_battery_data_available = true;
        
        Log("Battery data updated - V:");
        Log(power_readings->battery_voltage);
        Log(" I:");
        Log(power_readings->battery_current);
        Log(" SOC:");
        Log(power_readings->battery_soc);
        break;
    case (uint32_t)JKBMS_DID::CELL_VOLT:
    case (uint32_t)JKBMS_DID::CELL_TEMP:
    case (uint32_t)JKBMS_DID::BATT_ST_2:
    case (uint32_t)JKBMS_DID::BMS_INFO:
    case (uint32_t)JKBMS_DID::BMS_SW_STATUS:
    case (uint32_t)JKBMS_DID::CTRL_INFO:
    case (uint32_t)JKBMS_DID::BMSCHG_INFO:
        is_battery_message = true;
        // Handle other JK BMS messages (these don't update main readings)
        break;
    default:
        break;
    }
    #endif
}

custom_msgs__msg__PowerReadings* get_power_readings() {
    return &g_power_readings;
}

bool is_battery_data_available() {
    return g_battery_data_available;
}

bool is_inverter_data_available() {
    return g_inverter_data_available;
}
