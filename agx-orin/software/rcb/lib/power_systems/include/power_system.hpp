#pragma once
#include <stdint.h>
#include "can.hpp"
#include "custom_msgs/msg/power_readings.h"

/**
 * @brief Power System Manager
 * 
 * This module coordinates both battery and inverter power systems,
 * retrieving data from both sources via CAN and managing the unified
 * PowerReadings message structure.
 */

/**
 * @brief Initialize the power system module
 */
void power_system_init();

/**
 * @brief Request power information from both battery and inverter
 * 
 * This function requests data from both the battery (JK BMS or Daly BMS)
 * and the inverter (Meanwell) systems via CAN.
 */
void request_power_system_information();

/**
 * @brief Parse incoming CAN messages for power system data
 * 
 * This function handles CAN messages from both battery and inverter systems,
 * updating the PowerReadings message structure accordingly.
 * 
 * @param can_id The CAN message ID
 * @param message The CAN message data buffer
 * @param power_readings Pointer to the PowerReadings message to update
 */
void parse_power_system_message(
    const uint32_t can_id,
    CanDevice::RecvDataBuffer_t message,
    custom_msgs__msg__PowerReadings* power_readings
);

/**
 * @brief Get the current power readings
 * 
 * @return Pointer to the current PowerReadings message
 */
custom_msgs__msg__PowerReadings* get_power_readings();

/**
 * @brief Check if battery data is available and valid
 * 
 * @return true if battery data is available, false otherwise
 */
bool is_battery_data_available();

/**
 * @brief Check if inverter data is available and valid
 * 
 * @return true if inverter data is available, false otherwise
 */
bool is_inverter_data_available();
