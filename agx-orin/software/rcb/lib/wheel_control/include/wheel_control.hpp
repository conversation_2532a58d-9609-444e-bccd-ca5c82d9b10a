#ifndef WHEEL_CONTROL_HPP
#define WHEEL_CONTROL_HPP
#include <stdint.h>
#include <cfloat>

#include "config.hpp"
#include "object_dictionary.hpp"
#include "utils.hpp"

// CAN Specific configurations

class wheelControl {
public:
  wheelControl(const uint8_t wheelID, const uint8_t travelMotorID,
               const uint8_t steerMotorID,
               const int32_t steeringEncoderOffset_Decimal,
               const uint16_t heatbeatTime_ms);
  ~wheelControl();
  void m_initializeWheel(const bool invertTravelMotorDirection,
                         const bool invertSteeringMotorDirection);

  void m_enableTravelMotor(bool enable);
  void m_enableSteerMotor(bool enable);
  void m_setProfileSpeedForSteerMotor_RPM(const float profileSpeed_RPM);
  void
  m_setProfileAccelerationForTravelMotor_RPS(const float profileAccel_RPS,
                                             const float profileDeaccel_RPS);
  void
  m_setProfileAccelerationForSteerMotor_RPS(const float profileAccel_RPS,
                                            const float profileDeaccel_RPS);
  void
  m_setAbsoluteSteerToAngle_Degrees(const float absoluteSteeringAngle_Degrees);
  void m_setTravelSpeed_RPM(const float travelSpeed);

  void m_setNodeHeartbeatTime(const uint8_t nodeID,
                              const uint16_t heartBeatTime_ms);

  void m_emergencyStopWheel();
  void m_requestErrorReadings();
  void m_requestEncoderReadings();
  auto parseTravelEncoderReading(
    const uint8_t message1,
    const uint8_t message2,
    const uint8_t message3,
    const uint8_t message4,
    int32_t &reading
  ) -> void;
  auto parseSteerEncoderReading(
    const uint8_t message1,
    const uint8_t message2,
    const uint8_t message3,
    const uint8_t message4,
    int32_t &reading
  ) -> void;
  auto parseTravelMotorError(
    const uint8_t LSB,
    const uint8_t MSB,
    uint16_t &travelMotorError
  ) -> bool;
  auto parseSteerMotorError(
    const uint8_t LSB,
    const uint8_t MSB,
    uint16_t &steerMotorError
  ) -> bool;



  static void prepareAndSendMessage(
        CanUtils::eOperationType operationType,
        uint8_t nodeID,
        CanUtils::eMessageDataType dataType,
        uint16_t index,
        uint8_t subIndex,
        uint32_t data
  );
  private:
  // For both motors
  const uint8_t m_wheelID;
  const uint16_t m_heartBeatTime_ms;

  // For Travel Motor
  const uint8_t m_travelMotorNodeID;
  const float m_travel_pos_vel_conversion_factor =
      (512.0f * TRAVEL_ENCODER_RESOLUTION * TRAVEL_MOTOR_GEAR_RATIO) / 1875.0;
  const float m_travel_acc_dec_conversion_factor =
      (65536.0f * TRAVEL_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;
  float m_currentTravelSpeed_RPM = 0.0f;
  uint8_t m_travelMotor_Direction = INVERT_DIR_CW;

  // For Steer Motor
  const uint8_t m_steerMotorNodeID;
  const float m_steering_pos_vel_conversion_factor =
      512.0f * STEERING_ENCODER_RESOLUTION / 1875.0;
  const float m_steering_acc_dec_conversion_factor =
      (65536.0f * STEERING_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;
  const int32_t m_steeringEncoderOffset_Decimal;
  float m_currentSteeringAngle_deg = FLT_MAX;
  int8_t m_invertSteering = 1;


  void m_setupTravelMotor(bool invertDirection);
  void m_setupSteeringMotor();
  void clearerror();
};
#endif // WHEEL_CONTROL_HPP
