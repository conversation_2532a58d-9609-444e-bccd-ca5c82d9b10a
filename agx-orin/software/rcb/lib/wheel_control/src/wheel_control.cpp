#include <cstdint>
#include <chrono>
#include <thread>


#include "wheel_control.hpp"
#include "object_dictionary.hpp"
#include "can.hpp"
#include "utils.hpp"
#include "logging.hpp"

#define TESTING_MODE_SPEED 500.0f
//-----------------------------------------------------------------//
// Class Constructor && Destructor
//-----------------------------------------------------------------//
/**
 * @brief Constructor for wheel class.
 * Controls 1 travel motor and 1 steer motor.
 *
 * @param travelMotorID NODE ID for travel motor. Less than 0x127
 * @param steerMotorID NODE ID for steer motor. Less than 0x127
 * @param invertTravelMotorDirection Invert Travel Motor Direction
 * @param invertSteeringMotorDirection Invert Steer Motor Direction
 * @param steeringEncoderOffset_Decimal Offset to zero the steer motor encoder
 * to counteract the encoder increments.
 * @param heatbeatTime_ms time to setup canopen heartbeat
 */
wheelControl::wheelControl(const uint8_t wheelID, const uint8_t travelMotorID,
                           const uint8_t steerMotorID,
                           const int32_t steeringEncoderOffset_Decimal,
                           const uint16_t heatbeatTime_ms)
    : m_wheelID(wheelID), m_heartBeatTime_ms(heatbeatTime_ms),
      m_travelMotorNodeID(travelMotorID), m_steerMotorNodeID(steerMotorID),
      m_steeringEncoderOffset_Decimal(steeringEncoderOffset_Decimal) {}

wheelControl::~wheelControl() = default;

//-----------------------------------------------------------------//
// Public Methods
//-----------------------------------------------------------------//

void wheelControl::m_initializeWheel(const bool invertTravelMotorDirection,
                                     const bool invertSteeringMotorDirection) {
  if (invertSteeringMotorDirection)
  {
    m_invertSteering = -1;
  }
  this->clearerror();
  this->m_setupTravelMotor(invertTravelMotorDirection); // Setup Travel Motor
  this->m_setupSteeringMotor();                         // Setup Steer Motor
}

void wheelControl::m_enableTravelMotor(bool enable) {
  uint8_t controlword = CONTROLWORD_SPEED_MODE_ENABLE;
  if (!enable) {
    controlword = CONTROLWORD_DRIVER_OFF;
  }

  this->prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD,
      m_travelMotorNodeID,
      CanUtils::eMessageDataType::U16,
      CONTROLWORD_IDX,
      CONTROLWORD_SUB_IDX,
      controlword
  );
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}

void wheelControl::m_enableSteerMotor(bool enable) {
  uint8_t controlword = CONTROLWORD_POSITION_MODE_ABSOLUTE_ENABLE;
  if (!enable) {
    controlword = CONTROLWORD_DRIVER_OFF;
  }

  this->prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD,
      m_steerMotorNodeID,
      CanUtils::eMessageDataType::U16,
      CONTROLWORD_IDX,
      CONTROLWORD_SUB_IDX,
      controlword
  );
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}
void wheelControl::m_setProfileSpeedForSteerMotor_RPM(
    const float profileSpeed_RPM) {
  this->m_enableSteerMotor(false);
  float profileSpeed_RPM_decimal =
      profileSpeed_RPM * m_steering_pos_vel_conversion_factor;
  this->prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD,
      m_steerMotorNodeID,
      CanUtils::eMessageDataType::U32,
      PROFILE_SPEED_IDX,
      PROFILE_SPEED_SUB_IDX,
      profileSpeed_RPM_decimal
  );
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}

void wheelControl::m_setProfileAccelerationForTravelMotor_RPS(
    const float profileAccel_RPS, const float profileDeaccel_RPS) {
  {
    float profileAccel_RPS_decimal =
        profileAccel_RPS * m_travel_acc_dec_conversion_factor;
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::U32,
        PROFILE_ACC_IDX,
        PROFILE_ACC_SUB_IDX,
        profileAccel_RPS_decimal
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
  {
    float profileDeaccel_RPS_decimal =
        profileDeaccel_RPS * m_travel_acc_dec_conversion_factor;
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::U32,
        PROFILE_DEC_IDX,
        PROFILE_DEC_SUB_IDX,
        profileDeaccel_RPS_decimal
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
}

void wheelControl::m_setProfileAccelerationForSteerMotor_RPS(
    const float profileAccel_RPS, const float profileDeaccel_RPS) {
  {
    float profileAccel_RPS_decimal =
        profileAccel_RPS * m_steering_acc_dec_conversion_factor;
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_steerMotorNodeID,
        CanUtils::eMessageDataType::U32,
        PROFILE_ACC_IDX,
        PROFILE_ACC_SUB_IDX,
        profileAccel_RPS_decimal
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
  {
    float profileDeaccel_RPS_decimal =
        profileDeaccel_RPS * m_steering_acc_dec_conversion_factor;
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_steerMotorNodeID,
        CanUtils::eMessageDataType::U32,
        PROFILE_DEC_IDX,
        PROFILE_DEC_SUB_IDX,
        profileDeaccel_RPS_decimal
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
}

void wheelControl::m_setAbsoluteSteerToAngle_Degrees(
    const float absoluteSteeringAngle_Degrees) {

  if (absoluteSteeringAngle_Degrees > AMR_MAX_STEER_ANGLE ||
      absoluteSteeringAngle_Degrees < -AMR_MAX_STEER_ANGLE) {
    // Serial.println("Cannot set the steer Angle. Out of bounds!!");
    return;
  }

  if (m_currentSteeringAngle_deg == absoluteSteeringAngle_Degrees)
    return;

  this->m_enableSteerMotor(false);

  const int32_t steerPosition_deg_decimal =
      (int32_t)(m_invertSteering * absoluteSteeringAngle_Degrees *
                m_steering_pos_vel_conversion_factor) +
      m_steeringEncoderOffset_Decimal;

  this->prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD,
      m_steerMotorNodeID,
      CanUtils::eMessageDataType::I32,
      TARGET_POSITION_IDX,
      TARGET_POSITION_SUB_IDX,
      steerPosition_deg_decimal
  );
  std::this_thread::sleep_for(CAN_MSG_DELAY);

  this->m_enableSteerMotor(true);
  m_currentSteeringAngle_deg = absoluteSteeringAngle_Degrees;
}

void wheelControl::m_setTravelSpeed_RPM(const float travelSpeed) {
#ifdef TESTING_MODE
  if (travelSpeed > TESTING_MODE_SPEED || travelSpeed < -TESTING_MODE_SPEED) {
    // Serial.println("Cannot set the Travel Speed. Out of bounds!!");
    return;
  }
#endif
  if (travelSpeed > AMR_MAX_TRAVEL_SPEED_RPM ||
      travelSpeed < -AMR_MAX_TRAVEL_SPEED_RPM) {
    // Serial.println("Cannot set the Travel Speed. Out of bounds!!");
    return;
  }

  const int32_t speed_rpm_decimal =
      (int32_t)(travelSpeed * m_travel_pos_vel_conversion_factor);

  {
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::I32,
        TARGET_SPEED_IDX,
        TARGET_SPEED_SUB_IDX,
        speed_rpm_decimal
    );
    m_currentTravelSpeed_RPM = travelSpeed;
  }
}

void wheelControl::m_setNodeHeartbeatTime(const uint8_t nodeID,
                                          const uint16_t heartBeatTime_ms) {
  this->prepareAndSendMessage(
      CanUtils::eOperationType::DOWNLOAD,
      m_travelMotorNodeID,
      CanUtils::eMessageDataType::U16,
      PRODUCER_HEARTBEAT_TIME_IDX,
      PRODUCER_HEARTBEAT_TIME_SUB_IDX,
      heartBeatTime_ms
  );
  std::this_thread::sleep_for(CAN_MSG_DELAY);
}

void wheelControl::m_emergencyStopWheel() {
  {
      this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::U16,
        CONTROLWORD_IDX,
        CONTROLWORD_SUB_IDX,
        CONTROLWORD_DRIVER_OFF
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
  {
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_steerMotorNodeID,
        CanUtils::eMessageDataType::U16,
        CONTROLWORD_IDX,
        CONTROLWORD_SUB_IDX,
        CONTROLWORD_DRIVER_OFF
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }
}

void wheelControl::m_requestErrorReadings() {
  {
    this->prepareAndSendMessage(
        CanUtils::eOperationType::SDO_READ,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::U16,
        ERROR_STATE_IDX,
        ERROR_STATE_SUB_IDX,
        0
    );
  }
  {
    this->prepareAndSendMessage(
        CanUtils::eOperationType::SDO_READ,
        m_steerMotorNodeID,
        CanUtils::eMessageDataType::U16,
        ERROR_STATE_IDX,
        ERROR_STATE_SUB_IDX,
        0
    );
  }
}

auto wheelControl::parseTravelMotorError(
    const uint8_t LSB,
    const uint8_t MSB,
    uint16_t &travelMotorError
    ) -> bool
{
  travelMotorError = (static_cast<uint16_t>(MSB) << 8) | LSB;
  return (travelMotorError > 0);
}

auto wheelControl::parseSteerMotorError(
    const uint8_t LSB,
    const uint8_t MSB,
    uint16_t &steerMotorError
    ) -> bool
{
  steerMotorError = (static_cast<uint16_t>(MSB) << 8) | LSB;
  return (steerMotorError > 0);
}

  /**
   * bits[0] -> Extended Error
   * bits[1] -> Encoder ABZ/not connected
   * bits[2] -> Encoder UVW/Encoder internal
   * bits[3] -> Encoder Counting/ Encoder CRC
   * bits[4] -> Driver Temperature
   * bits[5] -> Over Voltage
   * bits[6] -> Under Voltage
   * bits[7] -> Over Current
   * bits[8] -> Chop Resistor
   * bits[9] -> Position Following
   * bits[10] -> Low Logic Voltage
   * bits[11] -> Motors or Driver IIt
   * bits[12] -> Over Frequency
   * bits[13] -> Motor Temperature
   * bits[14] -> Motor Communication
   * bits[15] -> EEPROM Data
   */


void wheelControl::m_requestEncoderReadings() {
  {
    this->prepareAndSendMessage(
        CanUtils::eOperationType::SDO_READ,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::I32,
        POS_ACTUAL_IDX,
        POS_ACTUAL_SUB_IDX,
        0
    );
  }
  {
    this->prepareAndSendMessage(
        CanUtils::eOperationType::SDO_READ,
        m_steerMotorNodeID,
        CanUtils::eMessageDataType::I32,
        POS_ACTUAL_IDX,
        POS_ACTUAL_SUB_IDX,
        0
    );
  }
}

auto wheelControl::parseSteerEncoderReading(
    const uint8_t message1,
    const uint8_t message2,
    const uint8_t message3,
    const uint8_t message4,
    int32_t &reading
    ) -> void
{
    const int32_t encoder_reading =
        CanUtils::interpret_can_bytes_to_decimal_big_endian(
            1.0f,
            0.0f,
            4,
            message1,
            message2,
            message3,
            message4
        );

    reading = encoder_reading - m_steeringEncoderOffset_Decimal;
}

auto wheelControl::parseTravelEncoderReading(
    const uint8_t message1,
    const uint8_t message2,
    const uint8_t message3,
    const uint8_t message4,
    int32_t &reading
    ) -> void
{
    const int32_t encoder_reading =
        CanUtils::interpret_can_bytes_to_decimal_big_endian(
            1.0f,
            0.0f,
            4,
            message1,
            message2,
            message3,
            message4
        );

    reading = encoder_reading;
}


//-----------------------------------------------------------------//
// Private Methods
//-----------------------------------------------------------------//

void wheelControl::prepareAndSendMessage(
        const CanUtils::eOperationType operationType,
        const uint8_t nodeID,
        const CanUtils::eMessageDataType dataType,
        const uint16_t index,
        const uint8_t subIndex,
        const uint32_t data) {
  CanUtils::populateCanMessageFields(
      CanDevice::getActuationCan()->getSendDataBuffer(),
      operationType,
      dataType,
      index,
      subIndex,
      data
      );
  CanDevice::getActuationCan()->sendLastPopulatedData(
      SEND_MESSAGE_CAN_IDENTIFIER + nodeID,
      0x08
  );
}

void wheelControl::m_setupTravelMotor(bool invertDirection = false) {
  // Serial.print("Travel Motor ID: 0x");
  // Serial.println(this->m_travelMotorNodeID, HEX);

  // Set Producer_Heartbeat_Time
  this->m_setNodeHeartbeatTime(this->m_travelMotorNodeID,
                               this->m_heartBeatTime_ms);

  this->m_travelMotor_Direction = (
          invertDirection
          ? INVERT_DIR_CW
          : INVERT_DIR_CCW
  );
  // Invert Motor Direction
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::U8,
        INVERT_DIR_IDX,
        INVERT_DIR_SUB_IDX,
        m_travelMotor_Direction
    );
  std::this_thread::sleep_for(CAN_MSG_DELAY);

  // Set Operation_Mode to Speed Control
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_travelMotorNodeID,
        CanUtils::eMessageDataType::I8,
        OPERATION_MODE_IDX,
        OPERATION_MODE_SUB_IDX,
        OPERATION_MODE_SPEED_RAMP
    );
  std::this_thread::sleep_for(CAN_MSG_DELAY);

  this->m_enableTravelMotor(true);
  // Serial.print("Travel Motor Setup Complete: ");
  // Serial.println(m_wheelID);
}

void wheelControl::m_setupSteeringMotor() {
  // Serial.print("Steering Motor ID: 0x");
  // Serial.println(this->m_steerMotorNodeID, HEX);
  // Set Producer_Heartbeat_Time
  this->m_setNodeHeartbeatTime(m_steerMotorNodeID, this->m_heartBeatTime_ms);
  // Set Operation_Mode to Position Control
  {
    this->prepareAndSendMessage(
        CanUtils::eOperationType::DOWNLOAD,
        m_steerMotorNodeID,
        CanUtils::eMessageDataType::I8,
        OPERATION_MODE_IDX,
        OPERATION_MODE_SUB_IDX,
        OPERATION_MODE_POSITION
    );
    std::this_thread::sleep_for(CAN_MSG_DELAY);
  }

  this->m_enableSteerMotor(false);
  // set Motor Profile Speed to 100RPM
  this->m_setProfileSpeedForSteerMotor_RPM(AMR_STEER_PROFILE_SPEED);
  this->m_setAbsoluteSteerToAngle_Degrees(0.0f);
  // Serial.print("Steer Motor Setup Complete: ");
  // Serial.println(m_wheelID);
}

void wheelControl::clearerror() {
    Log("Steering Motor ID: 0x");
    Log(this->m_steerMotorNodeID);
    // Set Control Word as 80 to clear driver error
    {
        this->prepareAndSendMessage(
            CanUtils::eOperationType::DOWNLOAD,
            m_steerMotorNodeID,
            CanUtils::eMessageDataType::I8,
            CONTROLWORD_IDX,
            CONTROLWORD_SUB_IDX,
            CONTROLWORD_ERROR_RESET
        );
        std::this_thread::sleep_for(CAN_MSG_DELAY);
    }
    {
        this->prepareAndSendMessage(
            CanUtils::eOperationType::DOWNLOAD,
            m_travelMotorNodeID,
            CanUtils::eMessageDataType::I8,
            CONTROLWORD_IDX,
            CONTROLWORD_SUB_IDX,
            CONTROLWORD_ERROR_RESET
        );
        std::this_thread::sleep_for(CAN_MSG_DELAY);
    }
    Log("Error Clear Complete: ");
    Log(m_wheelID);
    this->m_enableSteerMotor(false);
    this->m_enableTravelMotor(false);
}
