#pragma once
#include "config.hpp"
#include "kinco_servo_control.hpp"

class LiftkitControl {
public:
  LiftkitControl(
          const uint8_t node_id,
          const int32_t motor_homing_offset_mm,
          const uint16_t heartbeatTime_ms,
          const float acc_dec_conversion_factor,
          const float pos_vel_conversion_factor)
          : m_liftkitMotor(
                node_id,
                // 1 rotation = 2mm
                motor_homing_offset_mm * (LIFTKIT_ENCODER_RESOLUTION / 2),
                heartbeatTime_ms,
                acc_dec_conversion_factor,
                pos_vel_conversion_factor,
                LIFTKIT_MIN_HEIGHT_MM,
                LIFTKIT_MAX_HEIGHT_MM)
  {}

  ~LiftkitControl() = default;

  void m_initialize(void) {
      m_liftkitMotor.setProfileSpeedRPM(LIFTKIT_SPEED_RPM);
      m_liftkitMotor.m_initializeMotor(false);
      m_goToHeightmm(0);
  }

  bool m_goToHeightmm(const int32_t height) {
    if (height > LIFTKIT_MAX_HEIGHT_MM || height < LIFTKIT_MIN_HEIGHT_MM) {
        return false;
    }

    m_liftkitMotor.setMotorEncoderCounts(height * LIFTKIT_ENC_PER_MM);
    return true;
  }

  float getCurrentHeightmm() {
      return static_cast<float>(m_liftkitMotor.getLatestEnc()) / LIFTKIT_ENC_PER_MM;
  }

  void parseEncoderValue(size_t len, uint8_t *data) {
      m_liftkitMotor.parseEncoderReading(len, data);
  }

  void requestEncoderReadings() {
      m_liftkitMotor.requestEncoderReadings();
  }

  void m_triggerBrakes() {}

private:
  kincoMotorPositionControlMode m_liftkitMotor;
};
