<?xml version="1.0"?>
<package format="3">
  <name>vision_msgs</name>
  <version>4.1.1</version>
  <description>
    Messages for interfacing with various computer vision pipelines, such as
    object detectors.
  </description>

  <maintainer email="<EMAIL>">Adam Allevato</maintainer>

  <license>Apache License 2.0</license>

  <author email="<EMAIL>">Adam Allevato</author>
  <maintainer email="<EMAIL>">Adam Allevato</maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>

  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>

  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
