#Goal Enums
# 0-2 Reserved
uint8 NAV_GO_TO = 3
uint8 CHECK_AUTO_READY = 4
uint8 SET_SLAM_MODE = 5
uint8 SET_LOCALIZATION_MODE = 6
uint8 MAP_INIT = 7
uint8 MAP_SAVE = 8
uint8 MAP_LOAD = 9
uint8 MAP_DELETE = 10
uint8 POSE_IN_TOLERANCE = 11
uint8 MAP_CACHE_START = 12
uint8 MAP_CACHE_END = 13
uint8 PUBLISH_CACHE_POSE = 14
uint8 RUN_POSE_OPTIMIZER = 15
uint8 DOCK_AMR = 16
uint8 UMB_MARK_SEQUENCE = 17

# Request
custom_msgs/ActionBlock action_block
uint8 goal
custom_msgs/KeyValue[] params  # Optional command-specific parameters
---
# Error Code Enums
uint8 SUCCESS = 0
uint8 UNKNOWN_FAILURE = 1
uint8 ABORTED = 2
uint8 PLANNING_FAILURE = 3
uint8 SLAM_FAILURE = 4

#Result
custom_msgs/ActionBlock action_block
uint8 goal
uint8 error_code
string reason

---
#Feedback
geometry_msgs/PoseStamped amr_pose
uint8 goal
