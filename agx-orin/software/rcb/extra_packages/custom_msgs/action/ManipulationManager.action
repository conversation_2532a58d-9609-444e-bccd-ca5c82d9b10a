#Goal Enums
# 0-2 Reserved for tools
uint8 GO_TO = 3
uint8 COVERAGE_SCAN = 4
uint8 CHECK_AUTO_READY = 5
uint8 HOME_ARM = 6
uint8 ALIGN_WITH_AB = 7
uint8 PLAN_AND_EXECUTE_TOOLING = 8
uint8 GLOBAL_SHUTTER_SCAN = 9
uint8 MOVE_TO = 10

# Request
custom_msgs/ActionBlock action_block
uint8 goal
string home_position
float64[] target_joint_positions
---
# Error Code Enums
uint8 SUCCESS = 0
uint8 FAILED = 1
uint8 ABORTED = 2
uint8 PLANNING_FAILURE = 3
uint8 EXECUTION_FAILURE = 4

#Result
geometry_msgs/PoseStamped end_effector_pose
uint8 error_code
string reason

---

# Feedback Enums
uint8 PLANNING_SUCCESS = 0
uint8 EXECUTING_WAYPOINTS = 1

#Feedback
uint8 feedback_enum
geometry_msgs/PoseStamped end_effector_pose
uint8 goal
