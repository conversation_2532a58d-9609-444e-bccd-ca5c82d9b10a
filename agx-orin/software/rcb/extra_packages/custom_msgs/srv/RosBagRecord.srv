# Request
# command enums
uint8 STOP=0
uint8 START=1


uint8 command
string[] topics  # List of topics to record, empty means all topics
uint16 duration  # Duration in seconds to record, 0 means indefinite
uint64 max_size  # Maximum size in bytes for the bag file, 0 means no limit
string bag_path  # Path of the bag file to save, if empty a default path will be used
string task_id   # Task ID of the bag which is recorded 
string zone_id   # Zone ID of the bag which is recorded 
---
# Response
bool success
string message  # Message indicating success or failure
