# Request - empty or optional filters
int32 limit # Maximum number of responses to return, 0 = no limit
---
# Response
bool success
string message
# Array of task responses with task details
string[] task_ids # UUID as string
string[] statuses # e.g., "INPROGRESS", "PENDING", "COMPLETED"
bool[] successes # Indicates if each service call was successful
string[] messages # Optional messages from the service
float64[] timestamps # Unix timestamps 