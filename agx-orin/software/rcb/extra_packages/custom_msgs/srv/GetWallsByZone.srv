# Request - zone identifier
string zone_id  # ID of the zone to get walls for
---
# Response
bool success
string message
# Wall data arrays
string[] wall_ids
string[] wall_names
float64[] centroid_x
float64[] centroid_y
float64[] centroid_z
float64[] normal_x
float64[] normal_y
float64[] normal_z
float64[] width
float64[] height
float64[] depth
float64[] confidence
# New fields for wall line representation
float64[] start_point_x
float64[] start_point_y
float64[] end_point_x
float64[] end_point_y
string[] properties  # JSON strings containing additional wall properties
