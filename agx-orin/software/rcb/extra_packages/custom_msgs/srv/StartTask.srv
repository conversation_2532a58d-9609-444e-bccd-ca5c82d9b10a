# Request fields MUST match the data needed by the ROS node handling tasks
# Names should ideally match the API request fields for clarity
string task_type      # e.g., "L4_SAND", "L4_SPRAY" (matches TaskType enum values)
string task_id        # e.g., "task_id": "123e4567-e89b-12d3-a456-************",
string zone_id        # UUID as string
string operator_id    # UUID as string
string wall_id        # UUID as string
# Add any other parameters the task execution node requires
---
# Response fields MUST match what the task execution node provides
string task_id        # The unique ID generated by the task system (UUID as string)
string status         # Initial status, e.g., "INPROGRESS", "PENDING" (matches TaskStatus enum values)
bool success          # Indicate if the service call itself was successful in *accepting* the task
string message        # Optional message from the service (e.g., "Task accepted", "Validation failed") 
