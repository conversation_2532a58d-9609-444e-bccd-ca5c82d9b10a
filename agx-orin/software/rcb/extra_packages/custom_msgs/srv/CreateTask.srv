# Request
string task_id        # ID of the task (optional, can be autogenerated)
string zone_id        # ID of the zone where the task will be executed
string task_type      # Type of task to be created
string[] wall_ids     # Array of wall IDs involved in the task

---
# Response
bool success           # Indicates if the task creation was successful
string message         # Additional information about the result
string task_id         # ID of the newly created task
string zone_id         # ID of the zone where the task is located
string task_type       # Type of the created task
string status          # Status of the task (e.g., "PENDING", "INPROGRESS")
