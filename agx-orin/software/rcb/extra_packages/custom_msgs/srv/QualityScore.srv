# Request
string task_id        # ID of the task to get quality score for

---
# Response
bool success           # Indicates if the request was successful
string message         # Additional information about the result
float64 initial_quality_score  # Initial quality score (0-100)
float64 final_quality_score    # Final quality score (0-100)
# Wall quality data - array of wall quality information
custom_msgs/WallQualityData[] data
