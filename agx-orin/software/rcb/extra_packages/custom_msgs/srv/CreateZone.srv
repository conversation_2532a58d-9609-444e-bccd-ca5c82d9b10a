# Request
string zone_name       # Name of the zone
string site_id         # ID of the site where the zone is located
string zone_id         # ID of the zone (optional, can be autogenerated)
string operator_id     # ID of the operator creating the zone

---
# Response
bool success           # Indicates if the zone creation was successful
string message         # Additional information about the result
string zone_id         # ID of the newly created zone
string site_id         # ID of the site where the zone is located
string zone_name       # Name of the created zone
string status          # Status of the zone (e.g., ACTIVE, INACTIVE)
