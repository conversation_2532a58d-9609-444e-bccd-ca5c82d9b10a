# Request fields
# Check ActionBlock message for goal enums and how to populate the
# the request
custom_msgs/ActionBlockSchema schema
custom_msgs/ActionBlock action_block

# Requested level, can be set by UI
# Can be called by behaviour when sending the request
# Check Schema for level enums and how to populate them
uint8 level

# Goal
# Check Schema for goal enums and how to populate them
uint8 goal
string task_id
string zone_id

# Wall ID for operations that need a specific wall
# Can be used instead of action_block.wall_id for simpler calls
string wall_id

# Action block generation configuration
# Used for GENERATE_AND_SAVE_ACTION_BLOCKS goal
string task_type
float64 width
float64 height
float64 overlap_x
float64 overlap_y
bool enable_overlap
bool is_fixed_size

# Normal direction for SET_NORMAL_DIRECTION goal
# Check Schema for normal direction enums (POSITIVE_NORMAL=0, NEGATIVE_NORMAL=1)
uint8 normal_direction

# Corner for SET_CORNER goal
# Currently only supports BOTTOM_LEFT=0
uint8 corner

# Auto clean map parameters for AUTO_CLEAN_MAP goal
# DBSCAN clustering parameters
float64 eps                    # Epsilon (cluster radius) for DBSCAN
uint32 min_samples            # Minimum samples for DBSCAN clustering
bool remove_noise             # Whether to remove noise points
bool dbscan_clustering_enabled # Whether to enable DBSCAN clustering


# Wall detection parameters for AUTO_DETECT_WALLS goal
float64 distance_threshold
uint32 ransac_n
uint32 num_iterations
uint32 min_points
uint32 max_walls
float64 vertical_tolerance
float64 min_wall_size
float64 containment_tolerance
float64 boundary_connectivity_threshold

# 4, 3D points for spatial operations
# Array of 4 Point3D messages representing 3D coordinates
custom_msgs/Point3D[] points_3d

# Wall update parameters for UPDATE_WALL goal
# Centroid point for wall positioning
custom_msgs/Point3D centroid
# Wall dimensions
float64 wall_width
float64 wall_height

# update action block status
# List of action block IDs to be processed
string[] action_block_ids
# List of updated statuses corresponding to the action block IDs
int8[] block_statuses

---
# Response fields
bool success                  # Whether the request was successful
string message                # Status message if any

uint8 level                # Level to be executed for the action block
custom_msgs/ActionBlock action_block # Action block to be executed

float64 task_progress