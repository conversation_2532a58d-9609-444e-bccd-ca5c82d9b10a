# SafetyStatus.msg

# Safety Levels
uint8 SAFETY_LEVEL_SAFE = 0 # Robot is in a safe state to operate
uint8 SAFETY_LEVEL_WARNING = 1 # Robot is in a warning state, caution advised, log warnings, and raise alerts in UI
uint8 SAFETY_LEVEL_CRITICAL = 2 # Robot is in a critical state, All operations will be halted and auto recovery will be attempted
uint8 SAFETY_LEVEL_EMERGENCY = 3 # Robot is in an emergency state, immediate human intervention required


uint8 safety_level          # Use one of the SAFETY_LEVEL_* constants
string status_message
builtin_interfaces/Time stamp # Timestamp of the last status update
