# Goal enums
uint8 CURRENT_JOB_AB=0
uint8 NAV_SUCCESS=1
uint8 NAV_CANCEL=2
uint8 NAV_ABORT=3
uint8 TOOLING_SUCCESS=4
uint8 TOOLING_CANCEL=5
uint8 TOOLING_ABORT=6
uint8 UNKNOWN_JOB_STATE=7
uint8 SET_TARGET_LEVEL=8
uint8 SET_TARGET_WALL=9

# level enums
uint8 L0=0
uint8 L1=1
uint8 L2=2
uint8 L3=3
uint8 L4=4
uint8 L5=5

# Header message to get timestamp data of the goal
std_msgs/Header header

# Bounding box of the action block, check the expected format in the header file
vision_msgs/BoundingBox3D block
uint8 segmentation_resolution_x
uint8 segmentation_resolution_y

# Action Block normal
geometry_msgs/Vector3 normal


# Wall ID
# 1. <PERSON>haviour can send this as a request
# 2. UI Manager can set the wall target
# 3. In response task manager can populate the wall id based on
# robot pose
unique_identifier_msgs/UUID wall_id

# Action block id
unique_identifier_msgs/UUID action_block_id


# Requested level, can be set by UI
# Can be called by behaviour when sending the request
uint8 level

# Goal
uint8 goal
