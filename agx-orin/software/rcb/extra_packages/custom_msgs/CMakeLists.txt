cmake_minimum_required(VERSION 3.8)
project(custom_msgs)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(builtin_interfaces REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(vision_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)

rosidl_generate_interfaces(${PROJECT_NAME}
  "action/ManipulationManager.action"
  "action/NavigationManager.action"
  "action/PerceptionManagerAction.action"
  "msg/Point2D.msg"
  "msg/Point3D.msg"
  "msg/ActionBlock.msg"
  "msg/ActionBlockSchema.msg"
  "msg/ErrorCodeApp.msg"
  "msg/SegmentedBlock.msg"
  "msg/MachineState.msg"
  "msg/SensorHeartbeat.msg"
  "msg/ArmStatus.msg"
  "msg/Position.msg"
  "msg/ToolStatus.msg"
  "msg/PowerReadings.msg"
  "msg/WheelEncoder.msg"
  "msg/WallCompletionData.msg"
  "msg/WallQualityData.msg"
  "msg/MapStatus.msg"
  "msg/CorrectionPose.msg"
  "msg/CorrectionSequence.msg"
  "msg/SafetyStatus.msg"
  "msg/KeyValue.msg"
  "msg/KeyValueArray.msg"
  "msg/Heartbeat.msg"
  "msg/NodeStatus.msg"
  "msg/DetailedNodeStatus.msg"
  "msg/TaskDetailInfo.msg"
  "msg/ActiveHealthInterrupt.msg"
  "msg/ActiveHealthInterruptArray.msg"
  "msg/ToolEnums.msg"
  "msg/EncoderData.msg"
  "msg/GlobalShutterParams.msg"
  "msg/ActionBlockInfo.msg"
  "srv/StartTeleop.srv"
  "srv/ActionBlockSegmentation.srv"
  "srv/CaptureImage.srv"
  "srv/DockRobot.srv"
  "srv/EOARoughnessMapper.srv"
  "srv/EOADeformityMask.srv"
  "srv/EOADeformityto3D.srv"
  "srv/ExperimentParams.srv"
  "srv/GetRobotPoses.srv"
  "srv/GetSurfaceQuality.srv"
  "srv/GetTaskResponses.srv"
  "srv/StartTask.srv"
  "srv/MoveLift.srv"
  "srv/InverterPower.srv"
  "srv/TaskPlannerActionBlock.srv"
  "srv/GenerateNavGoal.srv"
  "srv/ToolCommand.srv"
  "srv/UIBehaviorInterface.srv"
  "srv/SetSteeringMode.srv"
  "srv/ModelCachedInference.srv"
  "srv/LoadZone.srv"
  "srv/CaptureCoverageData.srv"
  "srv/SelectWall.srv"
  "srv/GetNextActionBlock.srv"
  "srv/SetDummyTransform.srv"
  "srv/SetDummyImage.srv"
  "srv/GetWallsByZone.srv"
  "srv/CreateZone.srv"
  "srv/CreateTask.srv"
  "srv/PauseTask.srv"
  "srv/GetZonePcdPath.srv"
  "srv/EOAFanWidthMapper.srv"
  "srv/EOAOversandingMapper.srv"
  "srv/EOAOversprayingMapper.srv"
  "srv/TaskDetails.srv"
  "srv/QualityScore.srv"
  "srv/CorrectionLoop.srv"
  "srv/RecoveryAppService.srv"
  "srv/HealthMonitorInterrupt.srv"
  "srv/Reconstruct3D.srv"
  "srv/RosBagRecord.srv"
  "srv/TriggerGlobalShutter.srv"
  "srv/GetActionBlocksByWall.srv"
  "srv/LoadZoneStartTask.srv"
  DEPENDENCIES std_msgs geometry_msgs sensor_msgs vision_msgs
)

ament_package()
