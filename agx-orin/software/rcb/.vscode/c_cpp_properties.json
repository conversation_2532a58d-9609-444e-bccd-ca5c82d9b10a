//
// !!! WARNING !!! AUTO-GENERATED FILE!
// PLEASE DO NOT MODIFY IT AND USE "platformio.ini":
// https://docs.platformio.org/page/projectconf/section_env_build.html#build-flags
//
{
    "configurations": [
        {
            "name": "PlatformIO",
            "includePath": [
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SPI",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/tower_light/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/tower_light/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/relay_control/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/relay_control/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/power_systems/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/power_systems/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/liftkit/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/liftkit/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/kinco/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/kinco/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/wheel_control/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/wheel_control/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/battery/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/battery/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/utils/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/utils/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/inverter/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/inverter/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/can/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/can/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/embedded_common/uros/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/embedded_common/uros/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/logging/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/logging",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/Embedded Template Library/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/Embedded Template Library",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/ACAN_T4/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/NativeEthernet/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FNET/src",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/libmicroros/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/platform_code",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/platform_code/arduino/native_ethernet",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/platform_code/arduino",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/include",
                "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/extra_packages",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/cores/teensy4",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ADC",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/AccelStepper/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_NeoPixel",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_STMPE610",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_VS1053",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_nRF8001",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_nRF8001/utility",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/AltSoftSerial",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Artnet",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Audio",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Audio/utility",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Bounce",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Bounce2/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/CapacitiveSensor",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/CryptoAccel/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/DS1307RTC",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/DmxSimple",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/DogLcd",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/EEPROM",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/EasyTransfer/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/EasyTransferI2C/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Encoder",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Encoder/utility",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Entropy",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Ethernet/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FastCRC",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FastLED/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexCAN",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexCAN_T4",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexIO_t4/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexiTimer2",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FreqCount",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FreqMeasure",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FreqMeasureMulti",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FrequencyTimer2",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ILI9341_t3",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ILI9488_t3/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/IRremote/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Keypad/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LedControl/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LedDisplay",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LiquidCrystal/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LiquidCrystalFast",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LittleFS/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LowPower",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/MFRC522/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/MIDI/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Metro",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/MsTimer2",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/NXPMotionSense",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/NXPMotionSense/utility",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/NativeEthernet/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/OSC",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/OctoWS2811",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/OneWire",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PS2Keyboard",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PS2Keyboard/utility",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PWMServo",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Ping",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PulsePosition",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/QuadEncoder",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/RA8875/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/RadioHead",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ResponsiveAnalogRead/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SD/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SPIFlash",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ST7735_t3/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SdFat/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SerialFlash",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Servo",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ShiftPWM",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Snooze/src",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SoftPWM",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SoftwareSerial",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TFT_ILI9163C",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Talkie",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TeensyThreads",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Time",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TimeAlarms",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TimerOne",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TimerThree",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TinyGPS",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Tlc5940",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TouchScreen",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/USBHost_t36",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/USBHost_t36/utility",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/UTFT",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/VirtualWire",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/WS2812Serial",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Wire",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Wire/utility",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/XBee",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/XPT2046_Touchscreen",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/i2c_t3",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ks0108",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ssd1351",
                "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/x10",
                ""
            ],
            "browse": {
                "limitSymbolsToIncludedHeaders": true,
                "path": [
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SPI",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/tower_light/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/tower_light/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/relay_control/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/relay_control/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/power_systems/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/power_systems/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/liftkit/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/liftkit/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/kinco/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/kinco/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/wheel_control/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/wheel_control/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/battery/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/battery/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/utils/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/utils/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/inverter/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/inverter/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/can/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/can/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/embedded_common/uros/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/embedded_common/uros/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/logging/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/lib/logging",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/Embedded Template Library/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/Embedded Template Library",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/ACAN_T4/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/NativeEthernet/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FNET/src",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/libmicroros/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/platform_code",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/platform_code/arduino/native_ethernet",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/.pio/libdeps/teensy41/micro_ros_platformio/platform_code/arduino",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/include",
                    "/home/<USER>/Desktop/10xconstruction/embedded-systems/agx-orin/software/rcb/extra_packages",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/cores/teensy4",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ADC",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/AccelStepper/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_NeoPixel",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_STMPE610",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_VS1053",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_nRF8001",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Adafruit_nRF8001/utility",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/AltSoftSerial",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Artnet",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Audio",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Audio/utility",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Bounce",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Bounce2/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/CapacitiveSensor",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/CryptoAccel/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/DS1307RTC",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/DmxSimple",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/DogLcd",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/EEPROM",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/EasyTransfer/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/EasyTransferI2C/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Encoder",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Encoder/utility",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Entropy",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Ethernet/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FastCRC",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FastLED/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexCAN",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexCAN_T4",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexIO_t4/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FlexiTimer2",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FreqCount",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FreqMeasure",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FreqMeasureMulti",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/FrequencyTimer2",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ILI9341_t3",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ILI9488_t3/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/IRremote/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Keypad/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LedControl/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LedDisplay",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LiquidCrystal/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LiquidCrystalFast",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LittleFS/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/LowPower",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/MFRC522/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/MIDI/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Metro",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/MsTimer2",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/NXPMotionSense",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/NXPMotionSense/utility",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/NativeEthernet/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/OSC",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/OctoWS2811",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/OneWire",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PS2Keyboard",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PS2Keyboard/utility",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PWMServo",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Ping",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/PulsePosition",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/QuadEncoder",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/RA8875/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/RadioHead",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ResponsiveAnalogRead/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SD/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SPIFlash",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ST7735_t3/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SdFat/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SerialFlash",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Servo",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ShiftPWM",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Snooze/src",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SoftPWM",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/SoftwareSerial",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TFT_ILI9163C",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Talkie",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TeensyThreads",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Time",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TimeAlarms",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TimerOne",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TimerThree",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TinyGPS",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Tlc5940",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/TouchScreen",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/USBHost_t36",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/USBHost_t36/utility",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/UTFT",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/VirtualWire",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/WS2812Serial",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Wire",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/Wire/utility",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/XBee",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/XPT2046_Touchscreen",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/i2c_t3",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ks0108",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/ssd1351",
                    "/home/<USER>/.platformio/packages/framework-arduinoteensy/libraries/x10",
                    ""
                ]
            },
            "defines": [
                "PLATFORMIO=60118",
                "__IMXRT1062__",
                "ARDUINO_TEENSY41",
                "NDEBUG",
                "ROS_DOMAIN_ID=13",
                "USB_SERIAL",
                "ARDUINO=10805",
                "TEENSYDUINO=159",
                "CORE_TEENSY",
                "F_CPU=600000000",
                "LAYOUT_US_ENGLISH",
                "MICRO_ROS_TRANSPORT_ARDUINO_NATIVE_ETHERNET=1",
                "MICRO_ROS_DISTRO_HUMBLE =1",
                ""
            ],
            "cppStandard": "gnu++17",
            "compilerPath": "/home/<USER>/.platformio/packages/toolchain-gccarmnoneeabi-teensy/bin/arm-none-eabi-gcc",
            "compilerArgs": [
                "-mthumb",
                "-mcpu=cortex-m7",
                "-mfloat-abi=hard",
                "-mfpu=fpv5-d16",
                ""
            ]
        }
    ],
    "version": 4
}
