#include <cstdio>
#include <cstdint>
#include <algorithm>

#include <unity.h>

#include "utils.hpp"
#include "kinco_can_open_object_dictionary.hpp"

void setUp(void) {
    // set stuff up here
}

void tearDown(void) {
    // clean stuff up here
}

void test_setDirCommand() {
    uint16_t travelMotorNodeID = 0x601;
    uint8_t buffer[8];
    uint8_t expectedCommandArray[] = {
        47,     // Command byte
        126,    // Index - 1
        96,     // Index - 0
        0x0,    // Sub-index
        0x1,    // data
        0x0,    // data
        0x0,    // data
        0x0     // data
    };
    CanUtils::populateCanMessageFields(
        buffer,
        CanUtils::eOperationType::DOWNLOAD,
        CanUtils::eMessageDataType::U8,
        INVERT_DIR_IDX,
        INVERT_DIR_SUB_IDX,
        1
    );
    for (auto i = 0; i < 8; i++) {
        std::printf("0x%x ", buffer[i]);
    }
    std::printf("\n");
    TEST_ASSERT_TRUE(
        std::equal(
            std::begin(expectedCommandArray),
            std::end(expectedCommandArray),
            std::begin(buffer)
        )
    );
}


void test_encoderReadCommand() {
    uint16_t travelMotorNodeID = 0x601;
    uint8_t buffer[8];
    uint8_t expectedCommandArray[] = {
        0x40,   // Command byte
        0x63,   // Index - 1
        0x60,   // Index - 0
        0x0,    // Sub-index
        0x0,    // data
        0x0,    // data
        0x0,    // data
        0x0     // data
    };
    CanUtils::populateCanMessageFields(
        buffer,
        CanUtils::eOperationType::SDO_READ,
        CanUtils::eMessageDataType::I32,
        POS_ACTUAL_IDX,
        POS_ACTUAL_SUB_IDX,
        0
    );
    for (auto i = 0; i < 8; i++) {
        std::printf("0x%x ", buffer[i]);
    }
    std::printf("\n");
    TEST_ASSERT_TRUE(
        std::equal(
            std::begin(expectedCommandArray),
            std::end(expectedCommandArray),
            std::begin(buffer)
        )
    );
}

void test_encoderReadResponse() {
    uint8_t sampleData[] = {67, 99, 96, 0, 73, 221, 59, 0};
    int32_t expectedValue = 3923273;
    int32_t processedValue = \
        CanUtils::interpret_can_bytes_to_decimal_big_endian(
            1.0f,
            0.0f,
            4,
            sampleData[4],
            sampleData[5],
            sampleData[6],
            sampleData[7]
        );

    std::printf("Expected value: %d\n", expectedValue);
    std::printf("Parsed value: %d\n", processedValue);
    TEST_ASSERT_TRUE(processedValue == expectedValue);
}

int main( int argc, char **argv) {
    UNITY_BEGIN();
    RUN_TEST(test_setDirCommand);
    RUN_TEST(test_encoderReadCommand);
    RUN_TEST(test_encoderReadResponse);
    UNITY_END();
}
