{"names": {"tracetools": {"cmake-args": ["-DTRACETOOLS_DISABLED=ON", "-DTRACETOOLS_STATUS_CHECKING_TOOL=OFF"]}, "rosidl_typesupport": {"cmake-args": ["-DROSIDL_TYPESUPPORT_SINGLE_TYPESUPPORT=ON"]}, "rcl": {"cmake-args": ["-DBUILD_TESTING=OFF", "-DRCL_COMMAND_LINE_ENABLED=OFF", "-DRCL_LOGGING_ENABLED=OFF"]}, "rcutils": {"cmake-args": ["-DENABLE_TESTING=OFF", "-DRCUTILS_NO_FILESYSTEM=ON", "-DRCUTILS_NO_THREAD_SUPPORT=ON", "-DRCUTILS_AVOID_DYNAMIC_ALLOCATION=ON"]}, "microxrcedds_client": {"cmake-args": ["-DUCLIENT_PIC=OFF"]}, "rmw_microxrcedds": {"cmake-args": ["-DRMW_UXRCE_MAX_NODES=1", "-DRMW_UXRCE_MAX_PUBLISHERS=10", "-DRMW_UXRCE_MAX_SUBSCRIPTIONS=10", "-DRMW_UXRCE_MAX_SERVICES=5", "-DRMW_UXRCE_MAX_CLIENTS=1", "-DRMW_UXRCE_MAX_HISTORY=4"]}}}